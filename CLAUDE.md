# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

### Development
```bash
pnpm dev         # Start development server with Turbopack (hot reload)
pnpm lint        # Run ESLint for code quality checks
```

### Building & Production
```bash
pnpm build       # Full production build (Contentlayer → SEO → Next.js)
pnpm start       # Start production server
pnpm analyze     # Analyze bundle size
```

### Content Management

#### Using Contentlayer2 (default)
```bash
pnpm contentlayer build    # Process MDX content files
pnpm generate:content      # Generate sitemap.xml and RSS feeds
pnpm generate:sitemap      # Generate sitemap only
pnpm generate:rss          # Generate RSS feeds only
```

#### Using Next.js MDX Remote
```bash
# Set environment variable
CONTENT_PROVIDER=next-mdx-remote

# Build commands
pnpm build:content        # Build MDX content (auto-detects provider)
pnpm dev                  # Development with watch mode
pnpm build               # Production build
```

### Database Operations
```bash
pnpm db:generate   # Generate Drizzle migrations
pnpm db:migrate    # Run database migrations
pnpm db:studio     # Open Drizzle Studio GUI
pnpm db:push       # Push schema changes to database
```

### Cloudflare Deployment
```bash
pnpm cf:preview    # Build and preview on Cloudflare
pnpm cf:deploy     # Build and deploy to Cloudflare
pnpm cf:upload     # Upload build to Cloudflare
```

## High-Level Architecture

### Tech Stack
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Content**: MDX with Contentlayer2 for static content
- **Database**: PostgreSQL with Drizzle ORM
- **Styling**: Tailwind CSS v4 with CSS-in-JS
- **UI Components**: Radix UI primitives
- **Internationalization**: next-intl (en, zh)
- **Documentation**: Fumadocs (separate from CMS)
- **Deployment**: Vercel/Cloudflare Workers compatible

### Project Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── [locale]/          # Internationalized routes
│   │   ├── (default)/     # Public pages layout
│   │   ├── (admin)/       # Admin pages layout
│   │   └── (docs)/        # Documentation layout
│   └── (legal)/           # Legal pages (no i18n)
├── services/              # Business logic services
│   └── content/           # Unified content management system
│       ├── core/          # Core service classes
│       ├── providers/     # CMS provider implementations
│       ├── modules/       # Business logic modules
│       ├── types/         # TypeScript interfaces
│       └── utils/         # Utility functions
├── components/            # Reusable React components
├── db/                    # Database schema and config
├── i18n/                  # Internationalization
└── lib/                   # Utility functions

content/                    # MDX content files
├── blogs/                 # Blog posts (en/zh)
├── products/              # Product pages (en/zh)
├── case-studies/          # Case studies (en/zh)
└── docs/                  # Documentation (excluded from CMS)
```

### Key Architectural Decisions

1. **Unified Content Service**: The project uses a unified content service architecture in `src/services/content/` that provides:
   - Provider-based abstraction supporting multiple CMS backends
   - Supports both Contentlayer2 and Next.js MDX Remote (next-mdx-remote)
   - Core service classes (ContentService, ProviderFactory, ConfigManager)
   - Business modules for language switching, SEO, and content relations
   - Provider selection via `CONTENT_PROVIDER` environment variable

2. **Content Processing Flow**:
   
   **Contentlayer2 (default)**:
   - MDX files in `content/` → Contentlayer2 processing → TypeScript types
   - Automatic language detection from file path (e.g., `blogs/en/post.mdx`)
   - Generated content available at `.contentlayer/generated/`
   
   **Next.js MDX Remote**:
   - MDX files in `content/` → Pre-compilation to JSON bundles → Runtime loading
   - Compiled content stored in `.mdx-compiled/`
   - Optimized for Cloudflare Workers (no file system access needed)
   - Development watch mode for auto-recompilation
   
   Both providers use unified API through `@/services/content` for all content operations

3. **Routing Strategy**:
   - Default language (en) has no prefix: `/blogs`
   - Other languages use prefix: `/zh/blogs`
   - Static content (MDX) and dynamic content (database) coexist

4. **Build Pipeline**:
   ```
   contentlayer build → generate:content → next build
   ```
   This ensures MDX processing happens before SEO file generation and Next.js build.

5. **Deployment Flexibility**:
   - Main branch: Standard Next.js for Vercel
   - Cloudflare branch: OpenNext.js adapter for Workers
   - Docker support for self-hosting

### Content Types

1. **Static Content** (MDX via Contentlayer2):
   - Blogs (`/blogs`)
   - Products (`/products`)
   - Case Studies (`/case-studies`)

2. **Dynamic Content** (Database via Drizzle):
   - Posts (`/posts`) - existing feature
   - User-generated content

3. **Documentation** (Fumadocs):
   - Separate from CMS system
   - Located in `content/docs/`

### Environment Configuration

Required environment variables:
```bash
# Core
NEXT_PUBLIC_WEB_URL         # For sitemap/RSS generation
DATABASE_URL                # PostgreSQL connection

# Content Provider
CONTENT_PROVIDER            # 'contentlayer2' (default) or 'next-mdx-remote'

# Auth
AUTH_SECRET                 # Next-auth secret
AUTH_URL                    # Auth callback URL

# Optional: Analytics, Storage, Payment
# See .env.example for complete list
```

### Performance Considerations

1. **Static Generation**: All MDX content is statically generated at build time
2. **Turbopack**: Development server uses Turbopack for faster HMR
3. **Image Optimization**: Next.js Image component with remote patterns
4. **Bundle Analysis**: Use `pnpm analyze` to monitor bundle size

### Development Workflow

1. **Content Development**:
   - Edit MDX files in `content/`
   - Dev server auto-reloads with Contentlayer watching
   - No manual rebuild needed during development

2. **Database Changes**:
   - Modify schema in `src/db/schema.ts`
   - Run `pnpm db:generate` then `pnpm db:migrate`

3. **Testing**:
   - Currently no automated tests configured
   - Manual testing via development server
   - See `docs/TESTING_GUIDE.md` for testing strategy

### Content Service API

The unified content service provides these main functions:

```typescript
import { 
  getContent,        // Get single content item
  getContentList,    // Get list of content items
  getAllContentSlugs,// Get all slugs for static generation
  getContentTitle,   // Get content title
  getAvailableLanguageVersions,  // Check language availability
  handleContentLanguageSwitch,   // Handle language switching
  generateSEOMetadata,          // Generate SEO metadata
  detectContentPage             // Detect content type from URL
} from '@/services/content'
```

### Important Notes

- **Content Providers**:
  - **Contentlayer2**: Default provider using v0.4.6, the community fork of Contentlayer
  - **Next.js MDX Remote**: Alternative provider optimized for Cloudflare Workers
  - Switch providers using `CONTENT_PROVIDER` environment variable
- **MDX Processing**: 
  - Contentlayer2: Automatic in dev mode, manual via `pnpm contentlayer build` for production
  - Next MDX Remote: Use `pnpm build:content` which auto-detects the provider
  - **⚠️ IMPORTANT**: Always quote time values in MDX frontmatter! gray-matter converts unquoted time formats to seconds (e.g., `5:30` → `330`, but `"5:30"` → `"5:30"`)
- **SEO Files**: Generate with `pnpm generate:content` before deployment
- **Multi-layout System**: Different layouts for admin, docs, and public pages
- **Cloudflare Deployment**: 
  - Use the `cloudflare` branch which includes OpenNext.js adapter
  - Next.js MDX Remote provider is optimized for Workers environment
- **Content Service**: All content operations go through `@/services/content` for consistency