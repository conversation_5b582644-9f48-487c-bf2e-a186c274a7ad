# 内容提供器选择指南

本指南帮助您选择适合项目需求的内容提供器（Content Provider）。

## 概述

ShipAny 支持两种内容提供器：

1. **Contentlayer2** - 构建时编译的 MDX 处理器
2. **Next-MDX-Remote** - 运行时解析的 MDX 处理器

两种提供器都完全兼容 Vercel 和 Cloudflare Workers，但各有优势。

## 快速选择建议

| 部署平台 | 推荐提供器 | 原因 |
|---------|----------|------|
| Vercel | Contentlayer2 | 最佳开发体验，类型安全，热重载 |
| Cloudflare Workers | Next-MDX-Remote | 针对边缘运行时优化，无需文件系统 |
| Docker/VPS | Contentlayer2 | 完整 Node.js 环境，最佳性能 |

## 详细对比

### Contentlayer2

**优点：**
- ✅ 构建时编译，运行时性能最佳
- ✅ 自动生成 TypeScript 类型
- ✅ 开发时热重载
- ✅ 完整的 IDE 智能提示
- ✅ 支持复杂的内容关系

**缺点：**
- ❌ 需要 Node.js 构建环境
- ❌ 构建产物较大
- ❌ 不适合动态内容源

**最适合：**
- 传统的 SSG/ISR 部署
- 需要类型安全的项目
- 内容结构复杂的应用

### Next-MDX-Remote

**优点：**
- ✅ 边缘运行时兼容
- ✅ 构建产物小
- ✅ 支持动态内容源
- ✅ 无需文件系统访问
- ✅ 更灵活的内容处理

**缺点：**
- ❌ 运行时解析有性能开销
- ❌ 无自动类型生成
- ❌ 开发体验稍差

**最适合：**
- 边缘/Worker 部署
- 需要动态内容的应用
- 构建环境受限的场景

## 配置方式

### 环境变量配置

在 `.env` 文件中设置：

```bash
# 使用 Contentlayer2（默认）
CONTENT_PROVIDER=contentlayer2

# 使用 Next-MDX-Remote
CONTENT_PROVIDER=next-mdx-remote
```

### 构建命令

两种提供器使用相同的构建命令：

```bash
# 开发模式
pnpm dev

# 构建
pnpm build

# 内容构建（自动检测提供器）
pnpm build:content
```

## 部署前检查

使用预检查脚本验证配置：

```bash
# Vercel 部署检查
tsx scripts/pre-deploy-check.ts vercel

# Cloudflare 部署检查
tsx scripts/pre-deploy-check.ts cloudflare

# Docker 部署检查
tsx scripts/pre-deploy-check.ts docker
```

## 性能考虑

### Contentlayer2 性能特征
- **构建时间**：较长（需要编译所有 MDX）
- **运行时性能**：最佳（预编译的 React 组件）
- **内存使用**：中等
- **包大小**：较大（包含编译后的代码）

### Next-MDX-Remote 性能特征
- **构建时间**：较短（仅预处理）
- **运行时性能**：良好（有解析开销）
- **内存使用**：较低
- **包大小**：较小（仅包含原始内容）

## 迁移指南

### 从 Contentlayer2 迁移到 Next-MDX-Remote

1. 修改环境变量：
   ```bash
   CONTENT_PROVIDER=next-mdx-remote
   ```

2. 运行内容构建：
   ```bash
   pnpm build:content
   ```

3. 重新部署应用

### 从 Next-MDX-Remote 迁移到 Contentlayer2

1. 修改环境变量：
   ```bash
   CONTENT_PROVIDER=contentlayer2
   ```

2. 运行内容构建：
   ```bash
   pnpm build:content
   ```

3. 重新部署应用

## 常见问题

### Q: 两种提供器的内容格式相同吗？
A: 是的，MDX 文件格式完全相同，可以无缝切换。

### Q: 切换提供器需要修改代码吗？
A: 不需要，内容服务层已经抽象了差异。

### Q: 可以在生产环境动态切换吗？
A: 不可以，提供器在构建时确定，切换需要重新部署。

### Q: 如何选择最适合的提供器？
A: 根据部署平台和项目需求：
- 优先考虑开发体验 → Contentlayer2
- 优先考虑边缘性能 → Next-MDX-Remote
- 两者都支持时，遵循推荐配置

## 技术实现细节

### 构建时选择机制

提供器选择在构建时通过 `provider-selector.ts` 实现：

```typescript
// 构建时根据环境变量选择
const PROVIDER = process.env.CONTENT_PROVIDER || 'contentlayer2'

// 动态导入对应的提供器
switch (PROVIDER) {
  case 'contentlayer2':
    const { Contentlayer2Provider } = await import('../providers/contentlayer2')
    // ...
  case 'next-mdx-remote':
    const { NextMDXRemoteProvider } = await import('../providers/next-mdx-remote')
    // ...
}
```

### Tree-shaking 优化

未使用的提供器会被 webpack 自动移除：
- 使用静态路径的动态导入
- 构建时常量折叠
- 死代码消除

### MDX 组件适配

MDX 渲染组件自动检测内容格式：

```typescript
// body.code → Contentlayer2 格式
if (content.body.code) {
  const Component = useMDXComponent(content.body.code)
  // ...
}

// body.raw → Next-MDX-Remote 格式
if (content.body.raw) {
  return <MarkdownRenderer source={content.body.raw} />
}
```

## 最佳实践

1. **开发环境**：使用 Contentlayer2 获得最佳开发体验
2. **生产环境**：根据部署平台选择合适的提供器
3. **CI/CD**：在构建脚本中明确指定 CONTENT_PROVIDER
4. **监控**：关注构建时间和运行时性能指标

## 相关文档

- [CMS 架构设计](./CMS架构设计.md)
- [MDX 内容完整指南](./MDX内容完整指南.md)
- [开发部署指南](../开发部署指南.md)