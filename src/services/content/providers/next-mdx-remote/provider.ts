/**
 * Next.js MDX Remote Provider Implementation
 * 
 * This provider implements the content provider interface using next-mdx-remote,
 * providing support for MDX content with pre-compilation for optimal performance
 * in edge environments like Cloudflare Workers.
 */

import type { 
  ContentProvider, 
  ContentItem, 
  ContentType,
  QueryOptions,
  ContentMetadata,
  LanguageVersion 
} from '../../types'
import type { NextMDXRemoteConfig } from './types'
import { DEFAULT_CONFIG } from './config'
import { getStaticContent } from './static-content'

/**
 * NextMDXRemoteProvider Class
 * 
 * Implements the ContentProvider interface using pre-compiled MDX content.
 * Designed for optimal performance in serverless and edge environments.
 */
export class NextMDXRemoteProvider implements ContentProvider {
  readonly name = 'next-mdx-remote'
  readonly version = '4.4.1'
  
  private contentMap: Map<string, ContentItem>
  private config: Required<NextMDXRemoteConfig>
  
  constructor(config?: Partial<NextMDXRemoteConfig>) {
    // Merge with default configuration
    this.config = { ...DEFAULT_CONFIG, ...config } as Required<NextMDXRemoteConfig>
    
    // Initialize content map
    this.contentMap = new Map()
    
    // Load compiled content
    this.loadCompiledContent()
  }
  
  /**
   * Load pre-compiled content from bundle files
   * 
   * In production/Workers environment, loads the minified bundle.
   * In development, loads the full bundle with source content.
   */
  private loadCompiledContent(): void {
    try {
      console.log(`[NextMDXRemoteProvider] Loading static content bundle...`)
      
      // Get the statically imported content
      const bundle = getStaticContent()
      
      console.log(`[NextMDXRemoteProvider] Bundle loaded, keys:`, Object.keys(bundle).length)
      
      // Convert bundle object to Map for O(1) lookups
      Object.entries(bundle).forEach(([key, content]) => {
        this.contentMap.set(key, content as ContentItem)
      })
      
      console.log(`[NextMDXRemoteProvider] Loaded ${this.contentMap.size} content items`)
      console.log(`[NextMDXRemoteProvider] Sample keys:`, Array.from(this.contentMap.keys()).slice(0, 3))
    } catch (error) {
      console.error('[NextMDXRemoteProvider] Failed to load content bundle:', error)
      console.error('[NextMDXRemoteProvider] Run "pnpm build:content" to generate content bundle')
    }
  }
  
  /**
   * Get a single content item by type, slug, and locale
   * 
   * @param type - Content type (blog, product, case-study)
   * @param slug - Content identifier
   * @param locale - Language locale (en, zh)
   * @returns Content item or null if not found
   */
  async getContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null> {
    const key = `${type}-${slug}-${locale}`
    console.log(`[NextMDXRemoteProvider] Getting content: ${key}`)
    const content = this.contentMap.get(key) as T || null
    console.log(`[NextMDXRemoteProvider] Found: ${content ? 'Yes' : 'No'}`)
    return content
  }
  
  /**
   * Get a list of content items with optional filtering and sorting
   * 
   * @param type - Content type to filter by
   * @param locale - Language locale to filter by
   * @param options - Query options for filtering and sorting
   * @returns Array of content items
   */
  async getContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: QueryOptions
  ): Promise<T[]> {
    console.log(`[NextMDXRemoteProvider] getContentList called with:`, { type, locale, options })
    console.log(`[NextMDXRemoteProvider] Total items in contentMap:`, this.contentMap.size)
    
    // Start with all items of the specified type and locale
    let items = Array.from(this.contentMap.values())
      .filter(item => item.type === type && item.lang === locale)
    
    console.log(`[NextMDXRemoteProvider] Items after type/locale filter:`, items.length)
    
    // Apply featured filter if specified
    if (options?.featured !== undefined) {
      items = items.filter(item => item.featured === options.featured)
    }
    
    // Apply tag filter if specified
    if (options?.tags && options.tags.length > 0) {
      items = items.filter(item => 
        item.tags && options.tags!.some(tag => item.tags!.includes(tag))
      )
    }
    
    // Apply author filter if specified
    if (options?.author) {
      items = items.filter(item => item.author === options.author)
    }
    
    // Apply sorting
    if (options?.sortBy) {
      const order = options.order === 'desc' ? -1 : 1
      items.sort((a, b) => {
        const aVal = a[options.sortBy!] || ''
        const bVal = b[options.sortBy!] || ''
        return order * (aVal > bVal ? 1 : aVal < bVal ? -1 : 0)
      })
    }
    
    // Apply limit
    if (options?.limit) {
      items = items.slice(0, options.limit)
    }
    
    return items as T[]
  }
  
  /**
   * Get all content slugs for static generation
   * 
   * @param type - Content type
   * @returns Array of slug and locale pairs
   */
  async getAllContentSlugs(
    type: ContentType
  ): Promise<Array<{ slug: string; locale: string }>> {
    return Array.from(this.contentMap.values())
      .filter(item => item.type === type)
      .map(item => ({ slug: item.slug, locale: item.lang }))
  }
  
  /**
   * Check if content exists
   * 
   * @param type - Content type
   * @param slug - Content identifier
   * @param locale - Language locale
   * @returns Boolean indicating existence
   */
  async contentExists(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<boolean> {
    const key = `${type}-${slug}-${locale}`
    return this.contentMap.has(key)
  }
  
  /**
   * Get content title without loading full content
   * 
   * @param type - Content type
   * @param slug - Content identifier
   * @param locale - Language locale
   * @returns Content title or null
   */
  async getContentTitle(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<string | null> {
    const content = await this.getContent(type, slug, locale)
    return content?.title || null
  }
  
  /**
   * Get content metadata
   * 
   * @param type - Content type
   * @param slug - Content identifier
   * @param locale - Language locale
   * @returns Content metadata or null
   */
  async getContentMetadata(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<ContentMetadata | null> {
    const content = await this.getContent(type, slug, locale)
    if (!content) return null
    
    // Calculate word count and reading time
    const wordCount = content.body.raw.split(/\s+/).length
    const readingTime = Math.ceil(wordCount / 200) // Assuming 200 words per minute
    
    return {
      wordCount,
      readingTime,
      publishedAt: content.publishedAt,
      updatedAt: content.createdAt,
      author: content.author,
      tags: content.tags || []
    }
  }
  
  /**
   * Get available language versions of content
   * 
   * @param type - Content type
   * @param slug - Content identifier
   * @returns Array of language versions
   */
  async getAvailableLanguages(
    type: ContentType,
    slug: string
  ): Promise<LanguageVersion[]> {
    const languages = ['en', 'zh'] // Supported languages
    const versions: LanguageVersion[] = []
    
    for (const lang of languages) {
      const content = await this.getContent(type, slug, lang)
      versions.push({
        lang,
        title: content?.title || '',
        url: content?.url || `/${lang === 'en' ? '' : lang + '/'}${type}s/${slug}`,
        available: !!content
      })
    }
    
    return versions
  }
  
  /**
   * Get all content for static site generation
   * 
   * @param type - The content type to retrieve
   * @returns Array of all content items of the specified type
   */
  async getContentForStaticGeneration<T extends ContentItem>(
    type: ContentType
  ): Promise<T[]> {
    const allContent: T[] = []
    
    // Iterate through all content items and filter by type
    for (const [key, content] of this.contentMap.entries()) {
      if (key.startsWith(`${type}-`)) {
        allContent.push(content as T)
      }
    }
    
    return allContent
  }
}